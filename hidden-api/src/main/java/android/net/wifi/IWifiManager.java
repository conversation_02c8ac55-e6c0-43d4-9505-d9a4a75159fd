package android.net.wifi;

import android.content.pm.ParceledListSlice;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;

import androidx.annotation.RequiresApi;

@SuppressWarnings("deprecation")
public interface IWifiManager extends IInterface {

    abstract class Stub extends Binder implements IWifiManager {
        public static IWifiManager asInterface(IBinder obj) {
            throw new RuntimeException("Stub!");
        }
    }

    //Android 12+
    @RequiresApi(31)
    com.android.wifi.x.com.android.modules.utils.ParceledListSlice<WifiConfiguration> getPrivilegedConfiguredNetworks(String packageName, String featureId, Bundle extras);

    //Android 11
    @RequiresApi(30)
    ParceledListSlice<WifiConfiguration> getPrivilegedConfiguredNetworks(String packageName, String featureId);

    //Android 10
    ParceledListSlice<WifiConfiguration> getPrivilegedConfiguredNetworks(String packageName);

    //Android 12+
    @RequiresApi(31)
    WifiManager.AddNetworkResult addOrUpdateNetworkPrivileged(WifiConfiguration config, String packageName);

    //Android 10 & 11
    int addOrUpdateNetwork(WifiConfiguration config, String packageName);
}