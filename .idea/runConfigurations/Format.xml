<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Format" type="GradleRunConfiguration" factoryName="Gradle" activateToolWindowBeforeRun="false">
    <ExternalSystemSettings>
      <option name="env">
        <map>
          <entry key="DEVELOPER_DIR" value="/Applications/Xcode.app/Contents/Developer" />
        </map>
      </option>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/app" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="ktfmtFormat" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>