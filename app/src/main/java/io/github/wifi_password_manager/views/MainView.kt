package io.github.wifi_password_manager.views

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.FileDownload
import androidx.compose.material.icons.filled.FileUpload
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.FloatingActionButtonMenu
import androidx.compose.material3.FloatingActionButtonMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.ToggleFloatingActionButton
import androidx.compose.material3.ToggleFloatingActionButtonDefaults.animateIcon
import androidx.compose.material3.TooltipBox
import androidx.compose.material3.TooltipDefaults
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.rememberTooltipState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import io.github.wifi_password_manager.ui.theme.WiFiPasswordManagerTheme
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun MainView() {
    val viewModel = koinViewModel<MainViewModel>()

    MainView(onEvent = viewModel::onEvent)
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun MainView(onEvent: (MainViewModel.Event) -> Unit) {
    var expanded by rememberSaveable { mutableStateOf(false) }

    BackHandler(enabled = expanded) { expanded = false }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = "Saved WiFi Networks") },
                actions = {
                    TooltipBox(
                        positionProvider = TooltipDefaults.rememberTooltipPositionProvider(),
                        tooltip = { Text(text = "Settings") },
                        state = rememberTooltipState(),
                    ) {
                        IconButton(onClick = {}) {
                            Icon(
                                imageVector = Icons.Filled.Settings,
                                contentDescription = "Settings",
                            )
                        }
                    }
                },
            )
        },
        floatingActionButton = {
            FloatingActionButtonMenu(
                expanded = expanded,
                button = {
                    ToggleFloatingActionButton(
                        checked = expanded,
                        onCheckedChange = { expanded = it },
                    ) {
                        val imageVector by remember {
                            derivedStateOf {
                                if (checkedProgress > 0.5f) Icons.Filled.Close
                                else Icons.Filled.Menu
                            }
                        }

                        Icon(
                            imageVector = imageVector,
                            contentDescription = "More options",
                            modifier = Modifier.animateIcon(checkedProgress = { checkedProgress }),
                        )
                    }
                },
            ) {
                FloatingActionButtonMenuItem(
                    icon = {
                        Icon(imageVector = Icons.Filled.FileDownload, contentDescription = "Import")
                    },
                    text = { Text(text = "Import") },
                    onClick = { onEvent(MainViewModel.Event.ImportData) },
                )

                FloatingActionButtonMenuItem(
                    icon = {
                        Icon(imageVector = Icons.Filled.FileUpload, contentDescription = "Export")
                    },
                    text = { Text(text = "Export") },
                    onClick = { onEvent(MainViewModel.Event.ExportData) },
                )
            }
        },
    ) { innerPadding ->
        PullToRefreshBox(
            modifier = Modifier.padding(innerPadding),
            isRefreshing = false,
            onRefresh = { onEvent(MainViewModel.Event.GetSavedNetworks) },
        ) {
            LazyColumn(modifier = Modifier.fillMaxSize()) {}
        }
    }
}

@PreviewScreenSizes
@Composable
private fun MainViewPreview() {
    WiFiPasswordManagerTheme { MainView(onEvent = {}) }
}
