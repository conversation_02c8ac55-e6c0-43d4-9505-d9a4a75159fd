package io.github.wifi_password_manager.views

import androidx.lifecycle.ViewModel
import io.github.wifi_password_manager.services.WifiService

class MainViewModel(private val wifiService: WifiService) : ViewModel() {
    data class State(
        val savedNetworks: List<String> = emptyList(),
        val isRefreshing: Boolean = false,
        val isLoading: Boolean = false,
    )

    sealed class Event {
        data object GetSavedNetworks : Event()

        data object ImportData : Event()

        data object ExportData : Event()
    }

    fun onEvent(event: Event) {
        when (event) {
            Event.GetSavedNetworks -> getSavedNetworks()
            Event.ImportData -> importData()
            Event.ExportData -> exportData()
        }
    }

    private fun getSavedNetworks() {
        wifiService.getPrivilegedConfiguredNetworks()
    }

    private fun importData() {
        // TODO: Implement import functionality
    }

    private fun exportData() {
        // TODO: Implement export functionality
    }
}
