@file:Suppress("DEPRECATION")

package io.github.wifi_password_manager.services

import android.content.AttributionSource
import android.content.Context
import android.net.wifi.IWifiManager
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import kotlinx.serialization.json.Json
import rikka.shizuku.Shizuku
import rikka.shizuku.ShizukuBinderWrapper
import rikka.shizuku.SystemServiceHelper

class WifiService(private val json: Json) {
    companion object {
        private const val TAG = "WifiService"

        private const val SHELL_PACKAGE = "com.android.shell"
    }

    private val wifiManager by lazy {
        SystemServiceHelper.getSystemService(Context.WIFI_SERVICE)
            .let(::ShizukuBinderWrapper)
            .let(IWifiManager.Stub::asInterface)
    }

    private val user
        get() =
            when (Shizuku.getUid()) {
                0 -> "root"
                1000 -> "system"
                2000 -> "shell"
                else -> throw IllegalArgumentException("Unknown <PERSON>zuku user ${Shizuku.getUid()}")
            }

    fun getPrivilegedConfiguredNetworks(): List<WifiConfiguration> {
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ->
                    wifiManager
                        .getPrivilegedConfiguredNetworks(
                            user,
                            SHELL_PACKAGE,
                            Bundle().apply {
                                putParcelable(
                                    "EXTRA_PARAM_KEY_ATTRIBUTION_SOURCE",
                                    AttributionSource::class
                                        .java
                                        .getConstructor(
                                            Int::class.java,
                                            String::class.java,
                                            String::class.java,
                                            Set::class.java,
                                            AttributionSource::class.java,
                                        )
                                        .newInstance(
                                            Shizuku.getUid(),
                                            SHELL_PACKAGE,
                                            SHELL_PACKAGE,
                                            null as Set<String>?,
                                            null,
                                        ),
                                )
                            },
                        )
                        ?.list

                Build.VERSION.SDK_INT >= Build.VERSION_CODES.R ->
                    wifiManager.getPrivilegedConfiguredNetworks(user, SHELL_PACKAGE)?.list

                else -> wifiManager.getPrivilegedConfiguredNetworks(user)?.list
            }.orEmpty()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting configured networks", e)
            emptyList()
        }
    }

    fun addOrUpdateNetwork(config: WifiConfiguration): Boolean {
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ->
                    wifiManager.addOrUpdateNetworkPrivileged(config, SHELL_PACKAGE)?.statusCode ==
                        WifiManager.AddNetworkResult.STATUS_SUCCESS

                else -> wifiManager.addOrUpdateNetwork(config, SHELL_PACKAGE) != -1
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding or updating network", e)
            false
        }
    }
}
